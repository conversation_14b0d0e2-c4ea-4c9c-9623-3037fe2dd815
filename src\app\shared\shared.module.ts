import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { PhotoUploadComponent } from './components/photo-upload/photo-upload.component';
import { LocationPickerComponent } from './components/location-picker/location-picker.component';
import { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ]
})
export class SharedModule { }
