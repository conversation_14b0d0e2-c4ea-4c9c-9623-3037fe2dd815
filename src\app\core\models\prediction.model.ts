export interface BCTVPrediction {
  id: string;
  location: GeoLocation;
  riskLevel: RiskLevel;
  riskScore: number; // 0-100
  factors: RiskFactor[];
  recommendations: string[];
  validFrom: Date;
  validUntil: Date;
  confidence: number; // 0-1
  createdAt: Date;
  updatedAt: Date;
}

export enum RiskLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MODERATE = 'moderate',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}

export interface RiskFactor {
  type: RiskFactorType;
  value: number;
  weight: number;
  description: string;
}

export enum RiskFactorType {
  HOST_PLANT_DENSITY = 'host_plant_density',
  BLH_POPULATION = 'blh_population',
  WEATHER_CONDITIONS = 'weather_conditions',
  HISTORICAL_OUTBREAKS = 'historical_outbreaks',
  PROXIMITY_TO_INFECTED_AREAS = 'proximity_to_infected_areas',
  SEASONAL_FACTORS = 'seasonal_factors'
}

export interface PredictionRequest {
  location: GeoLocation;
  radius: number; // in kilometers
  timeframe: number; // in days
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  address?: string;
}

// Rule-based prediction parameters
export interface PredictionRules {
  hostPlantDensityThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  blhDensityThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  weatherFactors: {
    optimalTemperatureRange: [number, number];
    optimalHumidityRange: [number, number];
    windSpeedThreshold: number;
  };
  seasonalFactors: {
    highRiskMonths: number[];
    moderateRiskMonths: number[];
  };
}
