import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="spinner-container" [class.overlay]="overlay">
      <div class="spinner-content">
        <div class="spinner" [class]="size"></div>
        <p class="spinner-text" *ngIf="message">{{message}}</p>
      </div>
    </div>
  `,
  styles: [`
    .spinner-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2rem;
    }

    .spinner-container.overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      z-index: 9999;
    }

    .spinner-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .spinner {
      border-radius: 50%;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      animation: spin 1s linear infinite;
    }

    .spinner.small {
      width: 20px;
      height: 20px;
      border-width: 2px;
    }

    .spinner.medium {
      width: 40px;
      height: 40px;
      border-width: 3px;
    }

    .spinner.large {
      width: 60px;
      height: 60px;
      border-width: 4px;
    }

    .spinner-text {
      margin: 0;
      color: #666;
      font-size: 0.875rem;
      text-align: center;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `]
})
export class LoadingSpinnerComponent {
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() message: string = '';
  @Input() overlay: boolean = false;
}
