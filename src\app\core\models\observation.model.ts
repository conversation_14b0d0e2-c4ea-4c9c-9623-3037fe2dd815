export interface Observation {
  id: string;
  userId: string;
  type: ObservationType;
  location: GeoLocation;
  timestamp: Date;
  photos?: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ObservationType {
  HOST_PLANT = 'host_plant',
  BLH_OBSERVATION = 'blh_observation',
  BCTV_SYMPTOMS = 'bctv_symptoms',
  ERADICATION_EFFORT = 'eradication_effort'
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  address?: string;
}

// Host Plant Observation
export interface HostPlantObservation extends Observation {
  type: ObservationType.HOST_PLANT;
  hostPlantData: {
    species: HostPlantSpecies;
    density: PlantDensity;
    healthStatus: PlantHealthStatus;
    growthStage: PlantGrowthStage;
    estimatedCount?: number;
    coverageArea?: number; // in square meters
  };
}

// BLH (Beet Leafhopper) Observation
export interface BLHObservation extends Observation {
  type: ObservationType.BLH_OBSERVATION;
  blhData: {
    adultCount?: number;
    nymphCount?: number;
    density: BLHDensity;
    behavior: BLHBehavior[];
    weatherConditions?: WeatherConditions;
  };
}

// BCTV Symptoms Observation
export interface BCTVSymptomsObservation extends Observation {
  type: ObservationType.BCTV_SYMPTOMS;
  bctvData: {
    hostPlantSpecies: HostPlantSpecies;
    symptomSeverity: SymptomSeverity;
    symptomTypes: BCTVSymptomType[];
    affectedPlantCount?: number;
    totalPlantCount?: number;
    symptomDescription?: string;
  };
}

// Eradication Effort Observation
export interface EradicationObservation extends Observation {
  type: ObservationType.ERADICATION_EFFORT;
  eradicationData: {
    method: EradicationMethod;
    targetSpecies: HostPlantSpecies[];
    areaSize: number; // in square meters
    effectiveness: EffectivenessRating;
    cost?: number;
    duration?: number; // in hours
    followUpRequired: boolean;
    followUpDate?: Date;
  };
}

// Enums for the 10 key BCTV host weeds
export enum HostPlantSpecies {
  RUSSIAN_THISTLE = 'russian_thistle', // Salsola tragus
  KOCHIA = 'kochia', // Bassia scoparia
  LAMBSQUARTERS = 'lambsquarters', // Chenopodium album
  PIGWEED = 'pigweed', // Amaranthus spp.
  SHEPHERDS_PURSE = 'shepherds_purse', // Capsella bursa-pastoris
  LONDON_ROCKET = 'london_rocket', // Sisymbrium irio
  PRICKLY_LETTUCE = 'prickly_lettuce', // Lactuca serriola
  MUSTARD = 'mustard', // Brassica spp.
  FILAREE = 'filaree', // Erodium cicutarium
  MALVA = 'malva' // Malva spp.
}

export enum PlantDensity {
  LOW = 'low', // < 10 plants per m²
  MEDIUM = 'medium', // 10-50 plants per m²
  HIGH = 'high', // > 50 plants per m²
  VERY_HIGH = 'very_high' // > 100 plants per m²
}

export enum PlantHealthStatus {
  HEALTHY = 'healthy',
  STRESSED = 'stressed',
  DISEASED = 'diseased',
  DYING = 'dying'
}

export enum PlantGrowthStage {
  SEEDLING = 'seedling',
  VEGETATIVE = 'vegetative',
  FLOWERING = 'flowering',
  FRUITING = 'fruiting',
  SENESCENT = 'senescent'
}

export enum BLHDensity {
  NONE = 'none',
  LOW = 'low', // 1-5 per plant
  MEDIUM = 'medium', // 6-15 per plant
  HIGH = 'high', // 16-30 per plant
  VERY_HIGH = 'very_high' // > 30 per plant
}

export enum BLHBehavior {
  FEEDING = 'feeding',
  MATING = 'mating',
  OVIPOSITING = 'ovipositing',
  DISPERSING = 'dispersing',
  RESTING = 'resting'
}

export enum SymptomSeverity {
  NONE = 'none',
  MILD = 'mild',
  MODERATE = 'moderate',
  SEVERE = 'severe',
  VERY_SEVERE = 'very_severe'
}

export enum BCTVSymptomType {
  LEAF_CURLING = 'leaf_curling',
  YELLOWING = 'yellowing',
  STUNTING = 'stunting',
  VEIN_CLEARING = 'vein_clearing',
  NECROSIS = 'necrosis',
  WILTING = 'wilting'
}

export enum EradicationMethod {
  MECHANICAL_REMOVAL = 'mechanical_removal',
  HERBICIDE_APPLICATION = 'herbicide_application',
  MOWING = 'mowing',
  CULTIVATION = 'cultivation',
  BIOLOGICAL_CONTROL = 'biological_control',
  INTEGRATED_APPROACH = 'integrated_approach'
}

export enum EffectivenessRating {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

export interface WeatherConditions {
  temperature: number; // Celsius
  humidity: number; // percentage
  windSpeed: number; // km/h
  windDirection?: string;
  precipitation?: number; // mm
  cloudCover?: number; // percentage
}
