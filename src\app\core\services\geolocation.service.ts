import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { GeoLocation } from '../models/observation.model';

@Injectable({
  providedIn: 'root'
})
export class GeolocationService {
  private readonly defaultOptions: PositionOptions = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 60000 // 1 minute
  };

  constructor() {}

  getCurrentPosition(options?: PositionOptions): Observable<GeoLocation> {
    if (!navigator.geolocation) {
      return throwError(() => new Error('Geolocation is not supported by this browser'));
    }

    const positionOptions = { ...this.defaultOptions, ...options };

    return from(
      new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, positionOptions);
      })
    ).pipe(
      map((position: GeolocationPosition) => ({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        altitude: position.coords.altitude || undefined
      })),
      catchError(error => {
        let errorMessage = 'Unknown geolocation error';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }

        return throwError(() => new Error(errorMessage));
      })
    );
  }

  watchPosition(options?: PositionOptions): Observable<GeoLocation> {
    if (!navigator.geolocation) {
      return throwError(() => new Error('Geolocation is not supported by this browser'));
    }

    const positionOptions = { ...this.defaultOptions, ...options };

    return new Observable<GeoLocation>(observer => {
      const watchId = navigator.geolocation.watchPosition(
        (position: GeolocationPosition) => {
          observer.next({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude || undefined
          });
        },
        (error: GeolocationPositionError) => {
          let errorMessage = 'Unknown geolocation error';

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }

          observer.error(new Error(errorMessage));
        },
        positionOptions
      );

      // Return cleanup function
      return () => {
        navigator.geolocation.clearWatch(watchId);
      };
    });
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      // Using a simple reverse geocoding service (you might want to use a more robust service)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
      );

      if (!response.ok) {
        throw new Error('Reverse geocoding failed');
      }

      const data = await response.json();
      return data.locality || data.city || `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  isLocationWithinBounds(
    location: GeoLocation,
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    }
  ): boolean {
    return (
      location.latitude >= bounds.south &&
      location.latitude <= bounds.north &&
      location.longitude >= bounds.west &&
      location.longitude <= bounds.east
    );
  }

  // California bounds for BCTV application
  isWithinCalifornia(location: GeoLocation): boolean {
    const californiaBounds = {
      north: 42.0095,
      south: 32.5343,
      east: -114.1312,
      west: -124.4096
    };

    return this.isLocationWithinBounds(location, californiaBounds);
  }
}
