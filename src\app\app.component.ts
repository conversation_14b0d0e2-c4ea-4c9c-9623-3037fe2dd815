import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { AuthService } from './core/services/auth.service';

@Component({
  selector: 'app-root',
  imports: [CommonModule, RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'BCTV Management System';
  isLoading = true;
  loadingMessage = 'Loading BCTV System...';

  constructor(private authService: AuthService) {}

  ngOnInit() {
    // Initialize the app with a brief loading period
    setTimeout(() => {
      this.isLoading = false;
    }, 1500);
  }
}
