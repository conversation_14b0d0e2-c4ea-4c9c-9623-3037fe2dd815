import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PredictionService } from '../../../core/services/prediction.service';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';
import { BCTVPrediction, RiskLevel, PredictionRequest } from '../../../core/models/prediction.model';

@Component({
  selector: 'app-prediction-view',
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent],
  template: `
    <div class="prediction-container">
      <div class="prediction-header">
        <h1>BCTV Risk Predictions</h1>
        <p>AI-powered risk assessment for BCTV outbreaks</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <div class="prediction-controls">
        <button 
          (click)="generatePrediction()" 
          class="btn btn-primary"
          [disabled]="isGenerating">
          <span *ngIf="!isGenerating">🔮 Generate Prediction</span>
          <app-loading-spinner *ngIf="isGenerating" size="small"></app-loading-spinner>
        </button>
        
        <button 
          (click)="getCurrentLocation()" 
          class="btn btn-secondary"
          [disabled]="isGettingLocation">
          <span *ngIf="!isGettingLocation">📍 Use Current Location</span>
          <app-loading-spinner *ngIf="isGettingLocation" size="small"></app-loading-spinner>
        </button>
      </div>

      <div class="prediction-content" *ngIf="currentPrediction; else noPrediction">
        <div class="risk-overview">
          <div class="risk-level-card" [class]="currentPrediction.riskLevel">
            <div class="risk-level-header">
              <h2>Risk Level</h2>
              <div class="risk-score">{{currentPrediction.riskScore}}/100</div>
            </div>
            <div class="risk-level-badge">
              {{getRiskLevelDisplay(currentPrediction.riskLevel)}}
            </div>
            <div class="confidence">
              Confidence: {{(currentPrediction.confidence * 100).toFixed(0)}}%
            </div>
          </div>

          <div class="location-info">
            <h3>📍 Location</h3>
            <p>{{currentPrediction.location.address || 
                (currentPrediction.location.latitude.toFixed(4) + ', ' + 
                 currentPrediction.location.longitude.toFixed(4))}}</p>
            <p class="validity">
              Valid: {{currentPrediction.validFrom | date:'short'}} - 
              {{currentPrediction.validUntil | date:'short'}}
            </p>
          </div>
        </div>

        <div class="risk-factors">
          <h3>🔍 Risk Factors</h3>
          <div class="factors-grid">
            <div class="factor-card" *ngFor="let factor of currentPrediction.factors">
              <div class="factor-header">
                <h4>{{getFactorTitle(factor.type)}}</h4>
                <div class="factor-value" [class]="getFactorLevel(factor.value)">
                  {{(factor.value * 100).toFixed(0)}}%
                </div>
              </div>
              <div class="factor-description">{{factor.description}}</div>
              <div class="factor-weight">Weight: {{(factor.weight * 100).toFixed(0)}}%</div>
            </div>
          </div>
        </div>

        <div class="recommendations">
          <h3>💡 Recommendations</h3>
          <div class="recommendation-list">
            <div class="recommendation-item" *ngFor="let recommendation of currentPrediction.recommendations">
              <div class="recommendation-icon">•</div>
              <div class="recommendation-text">{{recommendation}}</div>
            </div>
          </div>
        </div>
      </div>

      <ng-template #noPrediction>
        <div class="no-prediction">
          <div class="placeholder-icon">🔮</div>
          <h2>No Prediction Available</h2>
          <p>Generate a BCTV risk prediction for your current location or a specific area.</p>
          <p>The prediction engine analyzes:</p>
          <ul>
            <li>Host plant density and distribution</li>
            <li>Beet leafhopper populations</li>
            <li>Weather conditions</li>
            <li>Seasonal factors</li>
            <li>Historical outbreak data</li>
          </ul>
        </div>
      </ng-template>

      <div class="error-message" *ngIf="errorMessage">
        {{errorMessage}}
      </div>
    </div>
  `,
  styles: [`
    .prediction-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .prediction-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .prediction-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .prediction-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .prediction-controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .prediction-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .risk-overview {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .risk-level-card {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }

    .risk-level-card.very_high { border-left: 6px solid #dc3545; }
    .risk-level-card.high { border-left: 6px solid #fd7e14; }
    .risk-level-card.moderate { border-left: 6px solid #ffc107; }
    .risk-level-card.low { border-left: 6px solid #28a745; }
    .risk-level-card.very_low { border-left: 6px solid #6c757d; }

    .risk-level-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .risk-level-header h2 {
      margin: 0;
      color: #333;
      font-size: 1.25rem;
    }

    .risk-score {
      font-size: 2rem;
      font-weight: 700;
      color: #007bff;
    }

    .risk-level-badge {
      font-size: 1.5rem;
      font-weight: 600;
      text-transform: uppercase;
      margin-bottom: 1rem;
    }

    .confidence {
      font-size: 0.875rem;
      color: #666;
    }

    .location-info {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .location-info h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .validity {
      font-size: 0.875rem;
      color: #666;
      margin-top: 0.5rem;
    }

    .risk-factors {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .risk-factors h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .factors-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
    }

    .factor-card {
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 1rem;
    }

    .factor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .factor-header h4 {
      margin: 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: #333;
    }

    .factor-value {
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
    }

    .factor-value.high { background: #dc3545; color: white; }
    .factor-value.medium { background: #ffc107; color: #333; }
    .factor-value.low { background: #28a745; color: white; }

    .factor-description {
      font-size: 0.75rem;
      color: #666;
      margin-bottom: 0.5rem;
    }

    .factor-weight {
      font-size: 0.625rem;
      color: #999;
    }

    .recommendations {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .recommendations h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .recommendation-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .recommendation-item {
      display: flex;
      gap: 0.75rem;
      align-items: flex-start;
    }

    .recommendation-icon {
      color: #007bff;
      font-weight: 600;
      margin-top: 0.125rem;
    }

    .recommendation-text {
      flex: 1;
      color: #333;
      line-height: 1.5;
    }

    .no-prediction {
      background: white;
      padding: 3rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }

    .placeholder-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .no-prediction h2 {
      color: #333;
      margin-bottom: 1rem;
    }

    .no-prediction ul {
      text-align: left;
      max-width: 400px;
      margin: 1.5rem auto;
    }

    .no-prediction li {
      margin-bottom: 0.5rem;
      color: #666;
    }

    .error-message {
      background: #fee;
      color: #c33;
      padding: 1rem;
      border-radius: 4px;
      border: 1px solid #fcc;
      margin-top: 1rem;
    }

    @media (max-width: 768px) {
      .prediction-container {
        padding: 1rem;
      }
      
      .risk-overview {
        grid-template-columns: 1fr;
      }
      
      .factors-grid {
        grid-template-columns: 1fr;
      }
      
      .prediction-controls {
        flex-direction: column;
      }
    }
  `]
})
export class PredictionViewComponent implements OnInit {
  currentPrediction: BCTVPrediction | null = null;
  isGenerating = false;
  isGettingLocation = false;
  errorMessage = '';

  constructor(
    private predictionService: PredictionService,
    private geolocationService: GeolocationService,
    private router: Router
  ) {}

  ngOnInit() {}

  async generatePrediction() {
    this.isGenerating = true;
    this.errorMessage = '';

    try {
      // Get current location first
      const location = await this.geolocationService.getCurrentPosition().toPromise();
      
      if (!location) {
        throw new Error('Could not get current location');
      }

      const request: PredictionRequest = {
        location,
        radius: 10, // 10km radius
        timeframe: 7 // 7 days
      };

      this.predictionService.generatePrediction(request).subscribe({
        next: (prediction) => {
          this.currentPrediction = prediction;
          this.isGenerating = false;
        },
        error: (error) => {
          console.error('Prediction error:', error);
          this.errorMessage = 'Failed to generate prediction. Please try again.';
          this.isGenerating = false;
        }
      });

    } catch (error) {
      console.error('Location error:', error);
      this.errorMessage = 'Could not get location. Please enable location services.';
      this.isGenerating = false;
    }
  }

  getCurrentLocation() {
    this.isGettingLocation = true;
    this.errorMessage = '';

    this.geolocationService.getCurrentPosition().subscribe({
      next: (location) => {
        console.log('Current location:', location);
        this.isGettingLocation = false;
        // Could update UI to show current location
      },
      error: (error) => {
        console.error('Location error:', error);
        this.errorMessage = 'Could not get current location.';
        this.isGettingLocation = false;
      }
    });
  }

  getRiskLevelDisplay(level: RiskLevel): string {
    switch (level) {
      case RiskLevel.VERY_HIGH: return 'Very High';
      case RiskLevel.HIGH: return 'High';
      case RiskLevel.MODERATE: return 'Moderate';
      case RiskLevel.LOW: return 'Low';
      case RiskLevel.VERY_LOW: return 'Very Low';
      default: return 'Unknown';
    }
  }

  getFactorTitle(type: string): string {
    switch (type) {
      case 'host_plant_density': return 'Host Plant Density';
      case 'blh_population': return 'BLH Population';
      case 'weather_conditions': return 'Weather Conditions';
      case 'seasonal_factors': return 'Seasonal Factors';
      case 'historical_outbreaks': return 'Historical Outbreaks';
      default: return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  }

  getFactorLevel(value: number): string {
    if (value >= 0.7) return 'high';
    if (value >= 0.4) return 'medium';
    return 'low';
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
