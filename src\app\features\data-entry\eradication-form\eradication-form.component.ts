import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SupabaseService } from '../../../core/services/supabase.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  HostPlantSpecies,
  EradicationMethod,
  EffectivenessRating,
  ObservationType,
  GeoLocation
} from '../../../core/models/observation.model';
import { PhotoUploadComponent } from '../../../shared/components/photo-upload/photo-upload.component';
import { LocationPickerComponent } from '../../../shared/components/location-picker/location-picker.component';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-eradication-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="form-container">
      <div class="form-header">
        <h1>Eradication Effort Observation</h1>
        <p>Record weed eradication and control activities</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <form [formGroup]="eradicationForm" (ngSubmit)="onSubmit()" class="observation-form">
        <!-- Location Section -->
        <div class="form-section">
          <h3>📍 Location Information</h3>
          <app-location-picker
            [required]="true"
            (locationSelected)="onLocationSelected($event)"
            (locationCleared)="onLocationCleared()">
          </app-location-picker>
        </div>

        <!-- Eradication Details -->
        <div class="form-section">
          <h3>🧹 Eradication Details</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="method">Eradication Method *</label>
              <select
                id="method"
                formControlName="method"
                class="form-control"
                [class.error]="isFieldInvalid('method')">
                <option value="">Select eradication method</option>
                <option value="mechanical_removal">Mechanical Removal (hand pulling, hoeing)</option>
                <option value="herbicide_application">Herbicide Application</option>
                <option value="mowing">Mowing</option>
                <option value="cultivation">Cultivation</option>
                <option value="biological_control">Biological Control</option>
                <option value="integrated_approach">Integrated Approach</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('method')">
                Eradication method is required
              </div>
            </div>

            <div class="form-group">
              <label for="effectiveness">Effectiveness Rating *</label>
              <select
                id="effectiveness"
                formControlName="effectiveness"
                class="form-control"
                [class.error]="isFieldInvalid('effectiveness')">
                <option value="">Select effectiveness rating</option>
                <option value="poor">Poor - Minimal impact, many weeds remain</option>
                <option value="fair">Fair - Some impact, moderate weed reduction</option>
                <option value="good">Good - Significant impact, most weeds controlled</option>
                <option value="excellent">Excellent - Complete or near-complete control</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('effectiveness')">
                Effectiveness rating is required
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>Target Species *</label>
            <div class="checkbox-group">
              <div class="checkbox-item" *ngFor="let species of hostPlantSpecies">
                <input
                  type="checkbox"
                  [id]="'species-' + species.value"
                  [value]="species.value"
                  (change)="onTargetSpeciesChange($event)">
                <label [for]="'species-' + species.value">{{species.label}}</label>
              </div>
            </div>
            <div class="error-message" *ngIf="selectedTargetSpecies.length === 0 && eradicationForm.get('targetSpecies')?.touched">
              At least one target species must be selected
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="areaSize">Area Size (m²) *</label>
              <input
                id="areaSize"
                type="number"
                formControlName="areaSize"
                class="form-control"
                [class.error]="isFieldInvalid('areaSize')"
                placeholder="Area treated in square meters"
                min="0"
                step="0.1">
              <div class="error-message" *ngIf="isFieldInvalid('areaSize')">
                Area size is required
              </div>
            </div>

            <div class="form-group">
              <label for="duration">Duration (hours)</label>
              <input
                id="duration"
                type="number"
                formControlName="duration"
                class="form-control"
                placeholder="Time spent on eradication"
                min="0"
                step="0.5">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="cost">Cost ($)</label>
              <input
                id="cost"
                type="number"
                formControlName="cost"
                class="form-control"
                placeholder="Total cost of eradication effort"
                min="0"
                step="0.01">
            </div>

            <div class="form-group">
              <label for="followUpRequired">Follow-up Required</label>
              <select
                id="followUpRequired"
                formControlName="followUpRequired"
                class="form-control"
                (change)="onFollowUpChange()">
                <option value="false">No</option>
                <option value="true">Yes</option>
              </select>
            </div>
          </div>

          <div class="form-group" *ngIf="eradicationForm.get('followUpRequired')?.value === 'true'">
            <label for="followUpDate">Follow-up Date</label>
            <input
              id="followUpDate"
              type="date"
              formControlName="followUpDate"
              class="form-control"
              [min]="minFollowUpDate">
          </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
          <h3>📝 Additional Information</h3>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              formControlName="notes"
              class="form-control"
              rows="4"
              placeholder="Equipment used, weather conditions, challenges encountered, specific herbicides used, etc.">
            </textarea>
          </div>
        </div>

        <!-- Photo Upload -->
        <div class="form-section">
          <h3>📷 Photos</h3>
          <p class="section-description">Upload before/after photos of the eradication effort</p>
          <app-photo-upload
            #photoUpload
            [maxFiles]="10"
            [bucket]="'eradication-observations'"
            (photosChanged)="onPhotosChanged($event)">
          </app-photo-upload>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="goBack()">
            Cancel
          </button>

          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="eradicationForm.invalid || isSubmitting || !selectedLocation || selectedTargetSpecies.length === 0">
            <span *ngIf="!isSubmitting">Save Observation</span>
            <app-loading-spinner *ngIf="isSubmitting" size="small"></app-loading-spinner>
          </button>
        </div>

        <div class="error-message" *ngIf="submitError">
          {{submitError}}
        </div>

        <div class="success-message" *ngIf="submitSuccess">
          Eradication effort observation saved successfully!
        </div>
      </form>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .form-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .form-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .observation-form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .form-section {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-section h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    .section-description {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 0.875rem;
      font-style: italic;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .form-control.error:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 0.75rem;
      margin-top: 0.5rem;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .checkbox-item input[type="checkbox"] {
      width: 16px;
      height: 16px;
      accent-color: #007bff;
    }

    .checkbox-item label {
      margin: 0;
      cursor: pointer;
      font-size: 0.875rem;
      color: #333;
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .success-message {
      color: #28a745;
      font-size: 0.875rem;
      margin-top: 1rem;
      padding: 0.75rem;
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .form-container {
        padding: 1rem;
      }

      .form-header,
      .form-section,
      .form-actions {
        padding: 1.5rem;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .checkbox-group {
        grid-template-columns: 1fr;
      }

      .form-actions {
        flex-direction: column;
      }
    }
  `]
})
export class EradicationFormComponent implements OnInit {
  @ViewChild('photoUpload') photoUpload!: PhotoUploadComponent;

  eradicationForm: FormGroup;
  selectedLocation: GeoLocation | null = null;
  selectedPhotos: File[] = [];
  selectedTargetSpecies: HostPlantSpecies[] = [];
  isSubmitting = false;
  submitError = '';
  submitSuccess = false;
  minFollowUpDate: string;

  hostPlantSpecies = [
    { value: HostPlantSpecies.RUSSIAN_THISTLE, label: 'Russian Thistle (Salsola tragus)' },
    { value: HostPlantSpecies.KOCHIA, label: 'Kochia (Bassia scoparia)' },
    { value: HostPlantSpecies.LAMBSQUARTERS, label: 'Lambsquarters (Chenopodium album)' },
    { value: HostPlantSpecies.PIGWEED, label: 'Pigweed (Amaranthus spp.)' },
    { value: HostPlantSpecies.SHEPHERDS_PURSE, label: 'Shepherd\'s Purse (Capsella bursa-pastoris)' },
    { value: HostPlantSpecies.LONDON_ROCKET, label: 'London Rocket (Sisymbrium irio)' },
    { value: HostPlantSpecies.PRICKLY_LETTUCE, label: 'Prickly Lettuce (Lactuca serriola)' },
    { value: HostPlantSpecies.MUSTARD, label: 'Mustard (Brassica spp.)' },
    { value: HostPlantSpecies.FILAREE, label: 'Filaree (Erodium cicutarium)' },
    { value: HostPlantSpecies.MALVA, label: 'Malva (Malva spp.)' }
  ];

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private authService: AuthService,
    private router: Router
  ) {
    // Set minimum follow-up date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.minFollowUpDate = tomorrow.toISOString().split('T')[0];

    this.eradicationForm = this.fb.group({
      method: ['', Validators.required],
      effectiveness: ['', Validators.required],
      areaSize: ['', [Validators.required, Validators.min(0)]],
      duration: [''],
      cost: [''],
      followUpRequired: ['false'],
      followUpDate: [''],
      notes: ['']
    });
  }

  ngOnInit() {}

  onLocationSelected(location: GeoLocation) {
    this.selectedLocation = location;
  }

  onLocationCleared() {
    this.selectedLocation = null;
  }

  onPhotosChanged(photos: File[]) {
    this.selectedPhotos = photos;
  }

  onTargetSpeciesChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    const value = checkbox.value as HostPlantSpecies;

    if (checkbox.checked) {
      if (!this.selectedTargetSpecies.includes(value)) {
        this.selectedTargetSpecies.push(value);
      }
    } else {
      this.selectedTargetSpecies = this.selectedTargetSpecies.filter(species => species !== value);
    }

    // Mark the targetSpecies field as touched for validation
    this.eradicationForm.get('targetSpecies')?.markAsTouched();
  }

  onFollowUpChange() {
    const followUpRequired = this.eradicationForm.get('followUpRequired')?.value === 'true';
    const followUpDateControl = this.eradicationForm.get('followUpDate');

    if (followUpRequired) {
      followUpDateControl?.setValidators([Validators.required]);
    } else {
      followUpDateControl?.clearValidators();
      followUpDateControl?.setValue('');
    }
    followUpDateControl?.updateValueAndValidity();
  }

  async onSubmit() {
    if (this.eradicationForm.valid && this.selectedLocation && this.selectedTargetSpecies.length > 0) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitSuccess = false;

      try {
        const currentUser = this.authService.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Upload photos first if any
        let photoUrls: string[] = [];
        if (this.selectedPhotos.length > 0) {
          photoUrls = await this.photoUpload.uploadPhotos();
        }

        const formValue = this.eradicationForm.value;

        // Create observation record
        const observation = {
          user_id: currentUser.id,
          type: ObservationType.ERADICATION_EFFORT,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          accuracy: this.selectedLocation.accuracy,
          address: this.selectedLocation.address,
          eradication_data: {
            method: formValue.method,
            targetSpecies: this.selectedTargetSpecies,
            areaSize: parseFloat(formValue.areaSize),
            effectiveness: formValue.effectiveness,
            cost: formValue.cost ? parseFloat(formValue.cost) : null,
            duration: formValue.duration ? parseFloat(formValue.duration) : null,
            followUpRequired: formValue.followUpRequired === 'true',
            followUpDate: formValue.followUpDate || null
          },
          photos: photoUrls,
          notes: formValue.notes,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { error } = await this.supabaseService.db
          .from('observations')
          .insert(observation);

        if (error) throw error;

        this.submitSuccess = true;

        // Reset form after successful submission
        setTimeout(() => {
          this.router.navigate(['/dashboard']);
        }, 2000);

      } catch (error) {
        console.error('Error saving observation:', error);
        this.submitError = 'Failed to save observation. Please try again.';
      } finally {
        this.isSubmitting = false;
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.eradicationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.eradicationForm.controls).forEach(key => {
      const control = this.eradicationForm.get(key);
      control?.markAsTouched();
    });

    // Also mark target species as touched
    this.eradicationForm.get('targetSpecies')?.markAsTouched();
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
